from collections import defaultdict

from flask import Flask, request, Response
from icalendar import Calendar

from schoology_api_helpers import *
from calendar_helpers import *

# ------------------ APP ------------------
app = Flask(__name__)

@app.get("/")
def proxy_ics():
    """
    /?url=<ICS_URL>[&all_day_1159=1]
    - Fetches an ICS, retimes Schoology assignment/event/discussion entries per COURSE_DUE_TIMES.
    - Optional: convert 11:59pm entries to true all-day before retiming.
    """
    src = request.args.get("url")
    if not src:
        abort(400, "Provide ?url=<ICS URL>")

    # Fetch ICS
    r = requests.get(src, timeout=30)
    if r.status_code != 200 or not r.content:
        abort(502, "Failed to fetch ICS")
    cal = Calendar.from_ical(r.content)

    convert_1159 = request.args.get("all_day_1159") in ("1", "true", "yes")
    assignment_stack_times = defaultdict(lambda: datetime.now(tz=CURRENT_TZ).replace(hour=8, minute=25))

    for ev in cal.walk("VEVENT"):
        # Optional: Promote 11:59pm placeholders to all-day first
        if convert_1159:
            dtstart = ev.get("DTSTART")
            dtend = ev.get("DTEND")
            sdt = dtstart.dt if dtstart else None
            edt = dtend.dt if dtend else None
            if any(isinstance(x, datetime) and x.hour == 23 and x.minute == 59 for x in (sdt, edt) if x):
                the_day = (sdt.date() if isinstance(sdt, datetime)
                           else edt.date() if isinstance(edt, datetime)
                else sdt if sdt else edt)
                if the_day:
                    as_all_day(ev, the_day)

        # Locate a Schoology URL in common fields
        fields = [
            str(ev.get("URL", "")),
            str(ev.get("DESCRIPTION", "")),
            str(ev.get("SUMMARY", "")),
            str(ev.get("LOCATION", "")),
        ]
        item_id = None

        for f in fields:
            # assignments / events
            m = RE_ASSIGN_OR_EVENT.search(f)
            if m:
                item_id = m.group("id")
                break
            # discussions
            m = RE_DISCUSSION.search(f)
            if m:
                item_id = m.group("id")
                break

        if not item_id:
            continue

        # Map item id -> section id -> course title
        sid = ITEM_ID_TO_SECTION.get(item_id)
        if not sid:
            continue
        course_title = SECTION_ID_TO_NAME.get(sid, sid)

        # Adjust DTSTART/DTEND time on this event
        dtstart = ev.get("DTSTART")
        if not dtstart:
            continue
        sdt = dtstart.dt.astimezone(CURRENT_TZ)
        ev["LOCATION"] = f"{course_title.split(' - ')[0]}"

        if STACK_EVENTS:
            key = sdt.date()
            set_due_time(ev, sdt, assignment_stack_times[key].time())
            assignment_stack_times[key] += EVENT_LENGTH
        else:
            desired = course_due_time(course_title)
            if desired:
                set_due_time(ev, sdt, desired)

        clean_description(ev)

    return Response(cal.to_ical(), mimetype="text/calendar; charset=utf-8")


# ------------------ MAIN ----------------------
if __name__ == "__main__":
    # Apple Calendar upgrades to HTTPS; run with TLS
    if not (Path(CERT_PATH).exists() and Path(KEY_PATH).exists()):
        raise SystemExit(
            f"Missing TLS cert or key.\n"
            f"Expected:\n  CERT_PATH={CERT_PATH}\n  KEY_PATH={KEY_PATH}\n"
            f"Tip (mkcert): mkcert 127.0.0.1  -> place files under certificates/"
        )
    app.run(HOST, PORT, debug=DEBUG, ssl_context=(CERT_PATH, KEY_PATH))
