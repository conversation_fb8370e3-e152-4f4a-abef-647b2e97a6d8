import re
from datetime import datetime, timedelta, time, timezone

from icalendar import vDatetime, vDate

from config import COURSE_DUE_TIMES, CURRENT_TZ, EVENT_LENGTH, RE_LINK_ASSIGN_OR_EVENT, RE_LINK_DISCUSSION


def course_due_time(course_title: str) -> time | None:
    """Return HH:MM as a tz-aware time in local tz, based on substring match."""
    for key, tstr in COURSE_DUE_TIMES.items():
        if key.lower() in course_title.lower():
            hh, mm = map(int, tstr.split(":"))
            return time(hour=hh, minute=mm, tzinfo=CURRENT_TZ)
    return None


def as_all_day(ev, day):
    ev["DTSTART"] = vDate(day)
    ev["DTEND"] = vDate(day + timedelta(days=1))
    if "DURATION" in ev:
        del ev["DURATION"]


def set_due_time(ev, dt, hhmm: time):
    """
    Set DTSTART/DTEND to the course-defined time on the event's *local date*,
    then emit in UTC. Default duration = 50 minutes.
    """

    def to_utc(local_dt: datetime) -> datetime:
        if local_dt.tzinfo is None:
            local_dt = local_dt.replace(tzinfo=CURRENT_TZ)
        return local_dt.astimezone(timezone.utc)

    if isinstance(dt, datetime):
        # Use the date in local tz
        local_date = dt.astimezone(CURRENT_TZ).date() if dt.tzinfo else dt.date()
        local_dt = datetime.combine(local_date, hhmm)  # hhmm has local tz
    else:
        # dt is date (all-day). Schedule on that local day.
        local_dt = datetime.combine(dt, hhmm)

    utc_start = to_utc(local_dt)
    utc_end = utc_start + EVENT_LENGTH

    ev["DTSTART"] = vDatetime(utc_start)
    ev["DTEND"] = vDatetime(utc_end)
    if "DURATION" in ev:
        del ev["DURATION"]


def clean_description(ev):
    desc = ev.get("DESCRIPTION", "")
    desc = re.sub(RE_LINK_ASSIGN_OR_EVENT, "", desc)
    desc = re.sub(RE_LINK_DISCUSSION, "", desc)
    ev["DESCRIPTION"] = desc
