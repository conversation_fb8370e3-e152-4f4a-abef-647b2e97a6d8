import time as _time

import requests
from flask import abort
from requests_oauthlib import OAuth1

from config import *


# ------------------ SCHOOL0GY API -------------
def oauth():
    if not (SCHO_CONSUMER_KEY and SCHO_CONSUMER_SECRET):
        abort(500, "Set SCHOOLOGY_KEY and SCHOOLOGY_SECRET env vars.")
    return OAuth1(SCHO_CONSUMER_KEY, SCHO_CONSUMER_SECRET)


def scho_get(path, params=None):
    r = requests.get(f"{SCHO_BASE}{path}", auth=oauth(), params=params, timeout=30)
    if r.status_code != 200:
        abort(502, f"Schoology API error {r.status_code} on {path}")
    return r.json()


def _cache_is_fresh(path: Path, max_age_secs: int) -> bool:
    if not path.exists():
        return False
    age = _time.time() - path.stat().st_mtime
    return age <= max_age_secs


def load_sections_and_items():
    """
    Returns (from cache or API):
      section_id_to_name: { sid(str): "Course Title - Section Title" }
      item_id_to_section: { item_id(str): sid(str) }  # assignments + events + discussions
    Cache file format (single dict):
      {
        "section_id_to_name": {...},
        "item_id_to_section": {...},
        "generated_at": "ISO8601"
      }
    """
    # Try fresh cache
    if _cache_is_fresh(CACHE_FILE, CACHE_MAX_AGE_SECS):
        try:
            cached = json.loads(CACHE_FILE.read_text())
            if isinstance(cached, dict) and "section_id_to_name" in cached and "item_id_to_section" in cached:
                print("Loaded Schoology data from fresh cache.")
                return cached["section_id_to_name"], cached["item_id_to_section"]
        except Exception as e:
            print("Cache read failed, will rebuild:", e)

    if not SCHO_USER_UID:
        abort(500, "Set SCHOOLOGY_UID to your Schoology numeric UID (string ok).")

    # Fetch sections (active enrollments)
    secs = scho_get(f"/users/{SCHO_USER_UID}/sections")
    section_id_to_name: dict[str, str] = {}
    for s in secs.get("section", []):
        sid = str(s.get("id"))
        course_title = s.get("course_title", "") or ""
        section_title = s.get("section_title", "") or ""
        title = f"{course_title} - {section_title}".strip(" -")
        if sid:
            section_id_to_name[sid] = title

    # Build item map: assignments + events + discussions
    item_id_to_section: dict[str, str] = {}
    for sid in list(section_id_to_name.keys()):
        # assignments
        try:
            lst = scho_get(f"/sections/{sid}/assignments", params={"limit": 200})
            for a in lst.get("assignment", []):
                aid = str(a.get("id") or "")
                if aid:
                    item_id_to_section[aid] = sid
        except Exception as e:
            print(f"Assignments fetch failed for section {sid}:", e)

        # events
        try:
            evs = scho_get(f"/sections/{sid}/events", params={"limit": 200})
            for e in evs.get("event", []):
                eid = str(e.get("id") or "")
                if eid:
                    item_id_to_section[eid] = sid
        except Exception as e:
            print(f"Events fetch failed for section {sid}:", e)

        # discussions
        try:
            # Many orgs expose discussions via /discussions
            dss = scho_get(f"/sections/{sid}/discussions", params={"limit": 200})
            for d in dss.get("discussion", []):
                did = str(d.get("id") or "")
                if did:
                    item_id_to_section[did] = sid
        except Exception as e:
            print(f"Discussions fetch failed for section {sid}:", e)

    # Write single cache dict
    cache_blob = {
        "section_id_to_name": section_id_to_name,
        "item_id_to_section": item_id_to_section,
        "generated_at": datetime.now().isoformat()
    }
    try:
        CACHE_FILE.write_text(json.dumps(cache_blob, indent=2))
        print("Wrote Schoology cache:", CACHE_FILE)
    except Exception as e:
        print("Failed to write cache:", e)

    return section_id_to_name, item_id_to_section


# Load Schoology maps at startup
print("Building Schoology data...")
SECTION_ID_TO_NAME, ITEM_ID_TO_SECTION = load_sections_and_items()
print("Local timezone:", CURRENT_TZ)
