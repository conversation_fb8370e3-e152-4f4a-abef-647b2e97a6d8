from collections import defaultdict

from flask import Flask, request, Response, redirect, url_for
from icalendar import Calendar

from schoology_api_helpers import *
from ical_helpers import *


try:
    import setproctitle
    setproctitle.setproctitle("sCal")
except Exception:
    pass

# ------------------ APP ------------------
app = Flask(__name__)


@app.get("/")
def proxy_ics():
    """
    /?url=<ICS_URL>[&all_day_1159=1]
    - Fetches an ICS, retimes Schoology assignment/event/discussion entries per COURSE_DUE_TIMES.
    - Optional: convert 11:59pm entries to true all-day before retiming.
    """
    src = request.args.get("url")
    if not src:
        abort(400, "Provide ?url=<ICS URL>")

    # Fetch ICS
    r = requests.get(src, timeout=30)
    if r.status_code != 200 or not r.content:
        abort(502, "Failed to fetch ICS")
    cal = Calendar.from_ical(r.content)

    convert_1159 = request.args.get("all_day_1159") in ("1", "true", "yes")
    assignment_stack_times = defaultdict(lambda: datetime.now(tz=CURRENT_TZ).replace(hour=8, minute=25))

    for ev in cal.walk("VEVENT"):
        # Optional: Promote 11:59pm placeholders to all-day first
        if convert_1159:
            dtstart = ev.get("DTSTART")
            dtend = ev.get("DTEND")
            sdt = dtstart.dt if dtstart else None
            edt = dtend.dt if dtend else None
            if any(isinstance(x, datetime) and x.hour == 23 and x.minute == 59 for x in (sdt, edt) if x):
                the_day = (sdt.date() if isinstance(sdt, datetime)
                           else edt.date() if isinstance(edt, datetime)
                else sdt if sdt else edt)
                if the_day:
                    as_all_day(ev, the_day)

        # Locate a Schoology URL in common fields
        fields = [
            str(ev.get("URL", "")),
            str(ev.get("DESCRIPTION", "")),
            str(ev.get("SUMMARY", "")),
            str(ev.get("LOCATION", "")),
        ]
        item_id = None
        item_type = None

        for f in fields:
            # assignments / events
            m = RE_ASSIGN_OR_EVENT.search(f)
            if m:
                item_id = m.group("id")
                item_type = m.group("type")
                break
            # discussions
            m = RE_DISCUSSION.search(f)
            if m:
                item_id = m.group("id")
                item_type = "discussion"
                break

        if not item_id:
            continue

        # Map item id -> section id -> course title
        sid = ITEM_ID_TO_SECTION.get(item_id)
        if not sid:
            continue
        course_title = SECTION_ID_TO_NAME.get(sid, sid)

        # Adjust DTSTART/DTEND time on this event
        dtstart = ev.get("DTSTART")
        if not dtstart:
            continue
        sdt = dtstart.dt.astimezone(CURRENT_TZ)
        ev["LOCATION"] = f"{course_title.split(' - ')[0]}"

        if STACK_EVENTS:
            key = sdt.date()
            desired = assignment_stack_times[key].time()
            assignment_stack_times[key] += EVENT_LENGTH
        else:
            desired = course_due_time(course_title)

        if desired:
            set_due_time(ev, sdt, desired)

        clean_description(ev, item_id, item_type)
        add_status_symbol(ev, sdt, item_id, item_type, sid, ASSIGNMENT_SUBMISSIONS, get_submission_status)

    return Response(cal.to_ical(), mimetype="text/calendar; charset=utf-8")


@app.get("/mark-done/<assignment_id>")
def mark_assignment_done(assignment_id):
    """
    Mark an assignment as done by storing it in the cache.
    This endpoint is called when the user clicks the "mark as done" link.
    """
    try:
        # Mark the assignment as manually completed
        mark_assignment_as_done(assignment_id)

        # Return a simple success page
        return f"""
        <html>
        <head><title>Assignment Marked as Done</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h2>✅ Assignment Marked as Done!</h2>
            <p>Assignment ID: {assignment_id}</p>
            <p>This assignment will now show as completed in your calendar.</p>
            <p><a href="javascript:window.close()">Close this window</a></p>
        </body>
        </html>
        """, 200
    except Exception as e:
        return f"""
        <html>
        <head><title>Error</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h2>❌ Error</h2>
            <p>Failed to mark assignment as done: {str(e)}</p>
            <p><a href="javascript:window.close()">Close this window</a></p>
        </body>
        </html>
        """, 500


# ------------------ MAIN ----------------------
if __name__ == "__main__":
    # Apple Calendar upgrades to HTTPS; run with TLS
    if not (Path(CERT_PATH).exists() and Path(KEY_PATH).exists()):
        raise SystemExit(
            f"Missing TLS cert or key.\n"
            f"Expected:\n  CERT_PATH={CERT_PATH}\n  KEY_PATH={KEY_PATH}\n"
            f"Tip (mkcert): mkcert 127.0.0.1  -> place files under certificates/"
        )
    app.run(HOST, PORT, debug=DEBUG, ssl_context=(CERT_PATH, KEY_PATH))
