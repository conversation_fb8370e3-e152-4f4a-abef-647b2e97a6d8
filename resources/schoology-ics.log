Sat Aug 16 23:46:19 PDT 2025 launched
Building Schoology data...
Wrote Schoology cache: schoology_cache.json
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
[2025-08-17 00:02:34,844] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x1039b6a70>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x1039b6a70>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 32, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x1039b6a70>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [17/Aug/2025 00:02:34] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [17/Aug/2025 00:02:54] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
