Sat Aug 16 11:52:03 PDT 2025 launched
Building Schoology data...
Wrote Schoology cache: cache/schoology_cache.json
Local timezone: PDT
 * Serving Flask app 'serve_schoology_ics'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
127.0.0.1 - - [16/Aug/2025 11:53:11] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [16/Aug/2025 16:28:22] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
Sat Aug 16 18:38:57 PDT 2025 launched
Building Schoology data...
Loaded Schoology data from fresh cache.
Local timezone: PDT
 * Serving Flask app 'serve_schoology_ics'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
